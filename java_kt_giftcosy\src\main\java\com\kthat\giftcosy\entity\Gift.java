package com.kthat.giftcosy.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "gifts")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class Gift {
    @Id
    Long id;
    String senderName;
    String receiverName;
    String relationShip;
    String style;

    @Column(columnDefinition = "TEXT")
    String massage;

    String imageUrl;
    String musicUrl;
    String animationType;
    String shareCode;

    boolean isActive = true;
    LocalDateTime createdAt;
    LocalDateTime updatedAt;
}
