import 'dart:ui';

class AppColors {
  static const primary = Color(0xFF6C63FF);        // Tím nh<PERSON>t – tư<PERSON><PERSON>, thân thiện
  static const secondary = Color(0xFFFF6584);      // Hồng đào – ấm áp, tình cảm

  static const background = Color(0xFFF9F9FB);     // <PERSON><PERSON><PERSON> nền rất sáng, không trắng toát
  static const cardBackground = Color(0xFFFFFFFF); // Card trắng nổi bật

  static const text = Color(0xFF1E1E1E);           // Đen nhẹ - dễ đọc
  static const textSecondary = Color(0xFF6B7280);  // Xám nhạt – phụ đề, label

  static const info = Color(0xFF3ABFF8);           // Xanh dương nhạt
  static const success = Color(0xFF22C55E);        // Xanh lá
  static const warning = Color(0xFFFACC15);        // Vàng chanh
  static const error = Color(0xFFEF4444);          // Đỏ rực – báo lỗi

  static const card = Color(0xFFF4F4F5);           // Card dạng block (khi không trắng)
  static const border = Color(0xFFE5E7EB);         // Viền nhạt
  static const divider = Color(0xFFE0E0E0);        // Gạch chia

  // Optional
  static const shadow = Color(0x1A000000);         // Bóng nhẹ
}
