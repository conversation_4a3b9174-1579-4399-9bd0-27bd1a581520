import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';



final GoRouter appRouter = GoRouter(
  initialLocation: '/',
  debugLogDiagnostics: true,

  // Optional: check login logic
  redirect: (context, state) {
    final isLoggedIn = false; // TODO: kiểm tra trạng thái đăng nhập
    final isLoggingIn = state.matchedLocation == '/login';

    if (!isLoggedIn && !isLoggingIn) return '/login';
    if (isLoggedIn && isLoggingIn) return '/home';
    return null;
  },

  // routes: [
  //   GoRoute(
  //     path: '/',
  //     name: 'splash',
  //     builder: (context, state) => const SplashScreen(),
  //   ),
  //   GoRoute(
  //     path: '/login',
  //     name: 'login',
  //     builder: (context, state) => const LoginScreen(),
  //   ),
  //   GoRoute(
  //     path: '/home',
  //     name: 'home',
  //     builder: (context, state) => const HomeScreen(),
  //   ),
  //   GoRoute(
  //     path: '/detail/:id',
  //     name: 'detail',
  //     builder: (context, state) {
  //       final id = state.pathParameters['id']!;
  //       return DetailScreen(id: id);
  //     },
  //   ),
  // ],
);
